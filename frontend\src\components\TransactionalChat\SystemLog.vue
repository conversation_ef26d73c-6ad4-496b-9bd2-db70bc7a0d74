<script setup lang="ts">
import { computed } from 'vue'
import type { FeedItem } from '@/stores/transactionalChat/transactionalChatStore'
import { useI18n } from 'vue-i18n'

interface Props {
  item: FeedItem
}

const props = defineProps<Props>()
const { t } = useI18n()

// Helper function to format amounts with proper currency formatting
const formatAmount = (amount: string | number, currency: string): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  if (currency === 'IRR' && numAmount >= 1000000) {
    const millions = numAmount / 1000000
    return `${millions.toFixed(1)}M ${currency}`
  }
  return `${numAmount.toLocaleString()} ${currency}`
}

// Standardized system message processing with pure i18n approach
const systemMessage = computed(() => {
  if (!props.item.message) return ''

  const message = props.item.message.trim()
  const data = props.item.data || {}

  // Check if it's a translation key format (namespace.key.subkey)
  const isTranslationKey = /^[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)+$/.test(message) &&
                          !message.includes(' ')

  if (isTranslationKey) {
    // Process data to format amounts properly
    const processedData = { ...data }

    // Format amounts in the data if they exist
    if (processedData.amount && processedData.currency) {
      processedData.amount = formatAmount(processedData.amount, processedData.currency)
    }

    // Handle multiple amount fields that might exist in system messages
    Object.keys(processedData).forEach(key => {
      if (key.includes('amount') || key.includes('Amount')) {
        const currencyKey = key.replace(/amount/i, 'currency').replace(/Amount/i, 'Currency')
        if (processedData[currencyKey]) {
          processedData[key] = formatAmount(processedData[key], processedData[currencyKey])
        }
      }
    })

    return t(message, processedData)
  }

  // Fallback: Log warning for non-translation-key messages and attempt direct translation
  console.warn('[SystemLog] Received non-translation-key message:', message)

  // Try to translate as-is in case it's a legacy key
  const translated = t(message, data)

  // If translation returns the same string, it means no translation was found
  if (translated === message) {
    // Return the original message but log that backend needs updating
    console.warn('[SystemLog] No translation found for message. Backend should send proper translation keys:', message)
    return message
  }

  return translated
})

const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

const messageTime = computed(() => formatTime(props.item.timestamp))

// Simplified celebration detection based on translation keys and emojis
const celebrationKeys = [
  'systemMessages.transaction.complete',
  'systemMessages.transaction.finalized',
  'systemMessages.step.transactionFinalized',
  'transactionalChat.systemLogs.transactionComplete'
]

const celebrationEmojis = ['🎉', '🎊', '✅', '🏆', '🌟', '💫']

const isCelebration = computed(() => {
  const originalMessage = props.item.message || ''
  const translatedMessage = systemMessage.value

  // Check if the original message is a celebration translation key
  const isCelebrationKey = celebrationKeys.some(key => originalMessage.includes(key))

  // Check for celebration emojis in either original or translated message
  const hasEmoji = celebrationEmojis.some(emoji =>
    originalMessage.includes(emoji) || translatedMessage.includes(emoji)
  )

  return isCelebrationKey || hasEmoji
})
</script>

<template>
  <div 
    class="system-log"
    :class="{ 'celebration': isCelebration }"
    data-testid="system-log"
  >
    <div class="system-content">
      <!-- System Icon -->
      <div 
        class="system-icon"
        data-testid="system-icon"
      >
        <span v-if="isCelebration" class="celebration-icon">🎉</span>
        <span v-else class="info-icon">ℹ️</span>
      </div>
      
      <!-- System Message -->
      <div class="system-message">
        <p 
          class="message-text"
          data-testid="system-message-text"
        >
          {{ systemMessage }}
        </p>
        
        <!-- Timestamp -->
        <span 
          class="message-timestamp"
          data-testid="system-message-timestamp"
        >
          {{ messageTime }}
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.system-log {
  display: flex;
  justify-content: center;
  margin: 16px 0;
  /* Remove initial opacity: 0 to prevent flash/disappear issues */
  animation: fadeIn 0.5s ease-out;
}

.system-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  background-color: var(--tc-chat-bubble-system);
  border: 1px solid var(--tc-border-light);
  border-radius: 12px;
  padding: 12px 16px;
  max-width: 80%;
  box-shadow: var(--tc-shadow-sm);
}

/* Celebration variant */
.celebration .system-content {
  background: linear-gradient(135deg, 
    rgba(16, 185, 129, 0.1), 
    rgba(34, 197, 94, 0.05)
  );
  border-color: var(--tc-success);
  animation: celebrationPulse 0.6s ease-out;
}

.system-icon {
  flex-shrink: 0;
  font-size: 16px;
  line-height: 1;
  margin-top: 1px;
}

.celebration-icon {
  animation: bounce 0.8s ease-out;
}

.info-icon {
  opacity: 0.7;
}

.system-message {
  flex: 1;
  min-width: 0;
}

.message-text {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
  color: var(--tc-text-secondary);
  font-weight: 500;
  text-align: center;
}

.celebration .message-text {
  color: var(--tc-success);
  font-weight: 600;
}

.message-timestamp {
  font-size: 11px;
  color: var(--tc-text-muted);
  display: block;
  text-align: center;
  margin-top: 4px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0.3;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes celebrationPulse {
  0% {
    transform: scale(1);
    box-shadow: var(--tc-shadow-sm);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: var(--tc-shadow-sm);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .system-log {
    margin: 12px 0;
  }
  
  .system-content {
    max-width: 90%;
    padding: 10px 12px;
    gap: 6px;
  }
  
  .system-icon {
    font-size: 14px;
  }
  
  .message-text {
    font-size: 13px;
  }
  
  .message-timestamp {
    font-size: 10px;
    margin-top: 3px;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .system-content {
    max-width: 95%;
    padding: 8px 10px;
    gap: 4px;
  }
  
  .message-text {
    font-size: 12px;
  }
  
  .system-icon {
    font-size: 13px;
  }
}

/* RTL Support */
[dir="rtl"] .system-content {
  direction: rtl;
}

[dir="rtl"] .message-text {
  text-align: center; /* Center alignment works for both directions */
}

[dir="rtl"] .message-timestamp {
  text-align: center;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .system-content {
    border-width: 2px;
    background-color: var(--tc-bg-card);
  }
  
  .message-text {
    color: var(--tc-text-primary);
  }
  
  .celebration .system-content {
    border-color: var(--tc-success);
    background-color: var(--tc-bg-card);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .system-log {
    animation: none;
    opacity: 1;
  }
  
  .celebration-icon {
    animation: none;
  }
  
  .celebration .system-content {
    animation: none;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}

/* Dark mode specific adjustments */
html.dark .system-content {
  background-color: var(--tc-bg-secondary);
}

html.dark .celebration .system-content {
  background: linear-gradient(135deg, 
    rgba(52, 211, 153, 0.15), 
    rgba(34, 197, 94, 0.08)
  );
}
</style>
