{"step": {"paymentInfoCompleted": "Payment information has been provided by both parties.", "negotiationStarted": "Payment information collection complete. First payer designation phase initiated.", "agreementReached": "First payer designation complete: {firstPayer} designated as initial payer.", "firstPaymentPhase": "First payment phase initiated. {firstPayer} to send {amount}.", "firstPaymentDeclared": "Payment declaration submitted by {username} for {amount}.", "firstPaymentConfirmed": "First payment receipt confirmed. Second payment phase initiated.", "secondPaymentPhase": "Second payment phase initiated. {secondPayer} to send {amount}.", "secondPaymentDeclared": "Payment declaration submitted by {username} for {amount}.", "secondPaymentConfirmed": "Second payment receipt confirmed. Transaction completed.", "transactionFinalized": "🎉 Transaction completed successfully."}, "proposal": {"from": "First payer proposal submitted by {username}: \"{message}\"", "agreed": "Proposal accepted by {username}.", "bothAgreed": "Mutual agreement reached. {firstPayer} designated as first payer. Payment deadline: {dueDate}.", "bothAgreedSystemDesignated": "Transaction terms accepted by both parties. {firstPayer} designated as first payer by system. Payment deadline: {dueDate}."}, "payment": {"declared": "Payment declaration submitted by {username} for {amount}. Receipt confirmation pending from {otherUser}.", "confirmedFirst": "First payment receipt confirmed by {username}. Second payment phase initiated for {payerUser} to {receiverUser}. Payment deadline: {dueDate}.", "confirmedSecond": "Second payment receipt confirmed by {username}. Transaction completed."}, "transaction": {"complete": "Transaction completed successfully. 🎉", "cancelled": "Transaction cancelled by {username}.", "disputed": "Transaction disputed by {username}."}}