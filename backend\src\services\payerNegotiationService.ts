import { PrismaClient, PayerNegotiation, PaymentReceivingInfo, ReceivingInfoStatus, NegotiationStatus, Transaction as PrismaTransaction, User as PrismaUser, Prisma } from '@prisma/client';
import { Server as SocketIOServer } from 'socket.io';
import { ChatService } from './chatService';
import { TransactionService } from './transactionService';
import { ILogger } from '../utils/logger';
import {
  NEGOTIATION_STATE_UPDATED,
  PAYMENT_INFO_SUBMITTED,
  PROPOSAL_MADE,
  NEGOTIATION_FINALIZED,
  PayerNegotiationStatePayload,
} from '../types/socketEvents';
import { PaymentReceivingInfoInput, MappedPaymentReceivingInfo } from '../types/payerNegotiation';
import { NotificationService } from './notificationService';
import { NotificationType } from '@prisma/client';

// Helper function to get username (simplified, adapt as needed)
function getUsernameFromTransactionContext(
    transaction: PrismaTransaction & { currencyAProvider?: Partial<PrismaUser> | null, currencyBProvider?: Partial<PrismaUser> | null } | null, 
    userIdToFind: string
  ): string {
  const defaultUsername = `User ${userIdToFind.substring(0, 6)}`;
  if (!transaction) return defaultUsername;

  if (transaction.currencyAProviderId === userIdToFind) {
    return transaction.currencyAProvider?.username || defaultUsername;
  }
  if (transaction.currencyBProviderId === userIdToFind) {
    return transaction.currencyBProvider?.username || defaultUsername;
  }
  return defaultUsername;
}

export enum PayerDesignationRule {
  REPUTATION = 'REPUTATION',
  CURRENCY = 'CURRENCY',
  OFFER_CREATOR = 'OFFER_CREATOR',
}

export interface SystemRecommendation {
  recommendedPayerId: string;
  appliedRule: PayerDesignationRule;
  reasoning: string;
  ruleDetails: Record<string, unknown>;
}

export class PayerNegotiationService {
  private prisma: PrismaClient;
  private chatService: ChatService;
  private io: SocketIOServer;
  private logger: ILogger;
  private transactionService: TransactionService;
  private notificationService: NotificationService;

  constructor(
    prismaClient: PrismaClient,
    chatService: ChatService,
    io: SocketIOServer,
    logger: ILogger,
    transactionService: TransactionService,
    notificationService: NotificationService
  ) {
    this.prisma = prismaClient;
    this.chatService = chatService;
    this.io = io;
    this.logger = logger;
    this.transactionService = transactionService;
    this.notificationService = notificationService;    this.logger.info("[PayerNegotiationService] Constructor called and dependencies injected.");
    if (!this.chatService) {
        this.logger.error("[PayerNegotiationService] CRITICAL: ChatService is undefined after injection!");
    }
    if (!this.io) {
        this.logger.error("[PayerNegotiationService] CRITICAL: Socket.IO server (io) is undefined after injection!");
    }
    if (!this.transactionService) {
        this.logger.error("[PayerNegotiationService] CRITICAL: TransactionService is undefined after injection!");
    }
    if (!this.notificationService) {
        this.logger.error("[PayerNegotiationService] CRITICAL: NotificationService is undefined after injection!");
    }
  }

  private async determineSystemRecommendedPayer(transactionId: string): Promise<SystemRecommendation> {
    const transaction = await this.prisma.transaction.findUnique({
      where: { id: transactionId },
      include: {
        currencyAProvider: {
          select: {
            id: true,
            reputationLevel: true,
            username: true,
            email: true,
          }
        },
        currencyBProvider: {
          select: {
            id: true,
            reputationLevel: true,
            username: true,
            email: true,
          }
        },
        offer: {
          select: {
            userId: true
          }
        }
      }
    });

    if (!transaction) {
      this.logger.error(`[PayerNegotiationService] Transaction not found for ID: ${transactionId}`);
      throw new Error('Transaction not found');
    }

    const fetchedProviderA = transaction.currencyAProvider;
    const fetchedProviderB = transaction.currencyBProvider;

    if (!fetchedProviderA || !fetchedProviderB) {
      this.logger.error(`[PayerNegotiationService] Transaction providers not found for TX ID: ${transactionId}`);
      throw new Error('Transaction providers not found');
    }

    const providerAName = fetchedProviderA.username || fetchedProviderA.email || `User ${fetchedProviderA.id.substring(0,6)}`;
    const providerBName = fetchedProviderB.username || fetchedProviderB.email || `User ${fetchedProviderB.id.substring(0,6)}`;

    if (fetchedProviderA.reputationLevel !== fetchedProviderB.reputationLevel) {
      const lowerRepUser = fetchedProviderA.reputationLevel < fetchedProviderB.reputationLevel ? fetchedProviderA : fetchedProviderB;
      const lowerRepUserName = lowerRepUser.id === fetchedProviderA.id ? providerAName : providerBName;
      return {
        recommendedPayerId: lowerRepUser.id,
        appliedRule: PayerDesignationRule.REPUTATION,
        reasoning: `${lowerRepUserName} has a lower reputation level (${lowerRepUser.reputationLevel}) and should pay first.`,
        ruleDetails: {
          reputationA: fetchedProviderA.reputationLevel,
          reputationB: fetchedProviderB.reputationLevel
        }
      };
    }

    // Note: Changed from 'RIAL' to 'IRR' to match your currency codes
    const localCurrencySymbol = 'IRR';
    if (transaction.currencyA === localCurrencySymbol || transaction.currencyB === localCurrencySymbol) {
      const localCurrencyProvider = transaction.currencyA === localCurrencySymbol ? fetchedProviderA : fetchedProviderB;
      const localCurrencyProviderName = localCurrencyProvider.id === fetchedProviderA.id ? providerAName : providerBName;
      return {
        recommendedPayerId: localCurrencyProvider.id,
        appliedRule: PayerDesignationRule.CURRENCY,
        reasoning: `${localCurrencyProviderName} is providing ${localCurrencySymbol} and should pay first.`,
        ruleDetails: {
          currencyA: transaction.currencyA,
          currencyB: transaction.currencyB,
          localCurrency: localCurrencySymbol
        }
      };
    }

    if (transaction.offer?.userId) {
        const offerCreator = transaction.offer.userId === fetchedProviderA.id ? fetchedProviderA : (transaction.offer.userId === fetchedProviderB.id ? fetchedProviderB : null) ;
        if (offerCreator) {
            const offerCreatorName = offerCreator.id === fetchedProviderA.id ? providerAName : providerBName;
            return {
                recommendedPayerId: offerCreator.id,
                appliedRule: PayerDesignationRule.OFFER_CREATOR,
                reasoning: `${offerCreatorName} created the offer and should pay first.`,
                ruleDetails: {
                isOfferCreator: true,
                offerCreatorId: offerCreator.id
                }
            };
        }
    }

    // Fallback logic
    this.logger.info(`[PayerNegotiationService] Applying fallback rule for TX ID: ${transactionId}. Defaulting to Provider A.`);
    return {
      recommendedPayerId: fetchedProviderA.id,
      appliedRule: PayerDesignationRule.OFFER_CREATOR,
      reasoning: `Defaulting to ${providerAName} (Provider A) as the first payer.`,
      ruleDetails: {
        isOfferCreator: false,
        defaultedToProviderA: true
      }
    };
  }

  private mapPaymentReceivingInfoToPayload(info: PaymentReceivingInfo | null): MappedPaymentReceivingInfo | null {
    if (!info) return null;
    return {
      id: info.id,
      bankName: info.bankName,
      accountNumber: info.accountNumber,
      accountHolderName: info.accountHolderName,
      isDefaultForUser: info.isDefaultForUser,
    };
  }

  private mapNegotiationToPayload(negotiation: PayerNegotiation & { partyA_PaymentReceivingInfo?: PaymentReceivingInfo | null, partyB_PaymentReceivingInfo?: PaymentReceivingInfo | null, transaction?: PrismaTransaction & { currencyAProvider?: Partial<PrismaUser> | null, currencyBProvider?: Partial<PrismaUser> | null } | null }): PayerNegotiationStatePayload {
    return {
      negotiationId: negotiation.negotiationId,
      transactionId: negotiation.transactionId,
      partyA_Id: negotiation.partyA_Id,
      partyB_Id: negotiation.partyB_Id,
      partyA_receivingInfoStatus: negotiation.partyA_receivingInfoStatus,
      partyB_receivingInfoStatus: negotiation.partyB_receivingInfoStatus,
      partyA_PaymentReceivingInfo: this.mapPaymentReceivingInfoToPayload(negotiation.partyA_PaymentReceivingInfo || null),
      partyB_PaymentReceivingInfo: this.mapPaymentReceivingInfoToPayload(negotiation.partyB_PaymentReceivingInfo || null),
      systemRecommendedPayerId: negotiation.systemRecommendedPayerId,
      systemRecommendationReason: negotiation.systemRecommendationReason,
      systemRecommendationRule: negotiation.systemRecommendationRule as PayerDesignationRule | null,
      systemRecommendationDetails: negotiation.systemRecommendationDetails as any,
      currentProposal_PayerId: negotiation.currentProposal_PayerId,
      currentProposal_ById: negotiation.currentProposal_ById,
      currentProposal_Message: negotiation.currentProposal_Message,
      partyA_agreedToCurrentProposal: negotiation.partyA_agreedToCurrentProposal,
      partyB_agreedToCurrentProposal: negotiation.partyB_agreedToCurrentProposal,
      negotiationStatus: negotiation.negotiationStatus,
      finalizedPayerId: negotiation.finalizedPayerId,
      paymentTimerDueDate: negotiation.paymentTimerDueDate?.toISOString() ?? null,
      createdAt: negotiation.createdAt.toISOString(),
      updatedAt: negotiation.updatedAt.toISOString(),
      isFinalOffer: negotiation.isFinalOffer,
    };
  }

  private determineInitialStatus(
    statusA: ReceivingInfoStatus,
    statusB: ReceivingInfoStatus
  ): NegotiationStatus {
    const a_isReady = statusA === ReceivingInfoStatus.PROVIDED || statusA === ReceivingInfoStatus.CONFIRMED_FROM_PROFILE;
    const b_isReady = statusB === ReceivingInfoStatus.PROVIDED || statusB === ReceivingInfoStatus.CONFIRMED_FROM_PROFILE;

    if (a_isReady && b_isReady) {
      return NegotiationStatus.READY_TO_NEGOTIATE;
    } else if (!a_isReady && b_isReady) {
      return NegotiationStatus.AWAITING_PARTY_A_RECEIVING_INFO;
    } else if (a_isReady && !b_isReady) {
      return NegotiationStatus.AWAITING_PARTY_B_RECEIVING_INFO;
    } else {
      return NegotiationStatus.PENDING_RECEIVING_INFO;
    }
  }

  async initializeNegotiation(transactionId: string): Promise<PayerNegotiationStatePayload> {
    const transaction = await this.prisma.transaction.findUnique({
      where: { id: transactionId },
      include: {
        currencyAProvider: { include: { paymentReceivingInfo: { where: { isDefaultForUser: true }, take: 1 } } },
        currencyBProvider: { include: { paymentReceivingInfo: { where: { isDefaultForUser: true }, take: 1 } } },
        offer: { select: { userId: true } }
      },
    });

    if (!transaction || !transaction.currencyAProviderId || !transaction.currencyBProviderId || !transaction.currencyAProvider || !transaction.currencyBProvider) {
      this.logger.error(`[PayerNegotiationService] Transaction or parties not found for TX ID: ${transactionId} during negotiation initialization.`);
      throw new Error('Transaction or parties (with full provider objects) not found for negotiation initialization.');
    }

    const partyA_Id = transaction.currencyAProviderId;
    const partyB_Id = transaction.currencyBProviderId;

    const partyA_DefaultInfo = transaction.currencyAProvider.paymentReceivingInfo?.[0];
    const partyB_DefaultInfo = transaction.currencyBProvider.paymentReceivingInfo?.[0];

    const partyA_receivingInfoStatus = partyA_DefaultInfo
      ? ReceivingInfoStatus.CONFIRMED_FROM_PROFILE
      : ReceivingInfoStatus.PENDING_INPUT;

    const partyB_receivingInfoStatus = partyB_DefaultInfo
      ? ReceivingInfoStatus.CONFIRMED_FROM_PROFILE
      : ReceivingInfoStatus.PENDING_INPUT;

    const systemRecommendation = await this.determineSystemRecommendedPayer(transactionId);
    let finalNegotiationStatus = this.determineInitialStatus(partyA_receivingInfoStatus, partyB_receivingInfoStatus);

    if (finalNegotiationStatus === NegotiationStatus.READY_TO_NEGOTIATE && systemRecommendation.recommendedPayerId) {
      finalNegotiationStatus = NegotiationStatus.PENDING_RESPONSE;
    }

    const newNegotiation = await this.prisma.payerNegotiation.create({
      data: {
        transactionId,
        partyA_Id,
        partyB_Id,
        partyA_receivingInfoStatus,
        partyB_receivingInfoStatus,
        partyA_PaymentReceivingInfoId: partyA_DefaultInfo?.id,
        partyB_PaymentReceivingInfoId: partyB_DefaultInfo?.id,
        systemRecommendedPayerId: systemRecommendation.recommendedPayerId,
        currentProposal_PayerId: systemRecommendation.recommendedPayerId,
        currentProposal_ById: 'system',
        systemRecommendationReason: systemRecommendation.reasoning,
        systemRecommendationRule: systemRecommendation.appliedRule,
        systemRecommendationDetails: systemRecommendation.ruleDetails ? JSON.stringify(systemRecommendation.ruleDetails) : null,
        negotiationStatus: finalNegotiationStatus,
      },
      include: {
        partyA_PaymentReceivingInfo: true,
        partyB_PaymentReceivingInfo: true,
      }
    });    const payload = this.mapNegotiationToPayload(newNegotiation);
    // Emit only to user rooms to avoid duplicates (users may be in both transaction and user rooms)
    this.io.to(partyA_Id).emit(NEGOTIATION_STATE_UPDATED, payload);
    this.io.to(partyB_Id).emit(NEGOTIATION_STATE_UPDATED, payload);

    this.logger.info(`[PayerNegotiationService] Initialized negotiation ${newNegotiation.negotiationId} for TX ${transactionId} with status ${finalNegotiationStatus}`);
    return payload;
  }

  async getOrInitializeNegotiationState(transactionId: string, userId: string): Promise<PayerNegotiationStatePayload> {
    const existingNegotiation = await this.prisma.payerNegotiation.findUnique({
      where: { transactionId },
      include: {
        partyA_PaymentReceivingInfo: true,
        partyB_PaymentReceivingInfo: true,
      },
    });

    if (existingNegotiation) {
      if (userId !== existingNegotiation.partyA_Id && userId !== existingNegotiation.partyB_Id) {
        this.logger.warn(`[PayerNegotiationService] User ${userId} not authorized for negotiation on TX ${transactionId}`);
        throw new Error('User is not authorized to view this negotiation state.');
      }
      this.logger.info(`[PayerNegotiationService] Fetched existing negotiation state for TX ${transactionId}`);
      return this.mapNegotiationToPayload(existingNegotiation);
    } else {
      this.logger.info(`[PayerNegotiationService] No existing negotiation for TX ${transactionId}, initializing...`);
      return this.initializeNegotiation(transactionId);
    }
  }  async submitReceivingInfo(
    negotiationId: string,
    userId: string,
    info: PaymentReceivingInfoInput
  ): Promise<PayerNegotiationStatePayload> {
    const negotiation = await this.prisma.payerNegotiation.findUnique({
      where: { negotiationId },
    });

    if (!negotiation) {
        this.logger.error(`[PayerNegotiationService] Negotiation not found: ${negotiationId} in submitReceivingInfo`);
        throw new Error('Negotiation not found');
    }
    if (userId !== negotiation.partyA_Id && userId !== negotiation.partyB_Id) {
        this.logger.warn(`[PayerNegotiationService] User ${userId} not part of negotiation ${negotiationId}`);
        throw new Error('User is not part of this negotiation');
    }

    const isPartyA = userId === negotiation.partyA_Id;
    let receivingInfoId: string | undefined = isPartyA ? negotiation.partyA_PaymentReceivingInfoId ?? undefined : negotiation.partyB_PaymentReceivingInfoId ?? undefined;    if (info.saveToProfile) {
      const dataToSave: Prisma.PaymentReceivingInfoUncheckedCreateInput | Prisma.PaymentReceivingInfoUpdateInput = {
        userId,
        bankName: info.bankName,
        accountNumber: info.accountNumber,
        accountHolderName: info.accountHolderName,
        isDefaultForUser: info.isDefaultForUser ?? false,
      };

      console.log('🔍 Backend saving payment info with:', {
        saveToProfile: info.saveToProfile,
        isDefaultForUser: dataToSave.isDefaultForUser,
        originalIsDefaultForUser: info.isDefaultForUser
      });

      if (info.id) {
        const updatedInfo = await this.prisma.paymentReceivingInfo.update({
          where: { id: info.id, userId },
          data: dataToSave as Prisma.PaymentReceivingInfoUpdateInput,
        });
        receivingInfoId = updatedInfo.id;
      } else {
        if (dataToSave.isDefaultForUser) {
          await this.prisma.paymentReceivingInfo.updateMany({
            where: { userId, isDefaultForUser: true },
            data: { isDefaultForUser: false },
          });
        }
        const newInfo = await this.prisma.paymentReceivingInfo.create({
          data: dataToSave as Prisma.PaymentReceivingInfoUncheckedCreateInput,
        });
        receivingInfoId = newInfo.id;
      }
    } else if (info.id) {
        receivingInfoId = info.id;
    }

    const updatedPartyAStatus = isPartyA
      ? ReceivingInfoStatus.PROVIDED
      : negotiation.partyA_receivingInfoStatus;
    const updatedPartyBStatus = !isPartyA
      ? ReceivingInfoStatus.PROVIDED
      : negotiation.partyB_receivingInfoStatus;

    let newNegotiationStatus = this.determineInitialStatus(updatedPartyAStatus, updatedPartyBStatus);
    
    if (newNegotiationStatus === NegotiationStatus.READY_TO_NEGOTIATE &&
        negotiation.currentProposal_ById === 'system' &&
        negotiation.currentProposal_PayerId) {
        newNegotiationStatus = NegotiationStatus.PENDING_RESPONSE;
    }

    const updateData: Prisma.PayerNegotiationUpdateInput = {
      partyA_receivingInfoStatus: updatedPartyAStatus,
      partyB_receivingInfoStatus: updatedPartyBStatus,
      negotiationStatus: newNegotiationStatus,
    };

    if (isPartyA) {
      updateData.partyA_PaymentReceivingInfo = receivingInfoId ? { connect: { id: receivingInfoId } } : (negotiation.partyA_PaymentReceivingInfoId ? { disconnect: true } : undefined);
    } else {
      updateData.partyB_PaymentReceivingInfo = receivingInfoId ? { connect: { id: receivingInfoId } } : (negotiation.partyB_PaymentReceivingInfoId ? { disconnect: true } : undefined);
    }

    const updatedNegotiation = await this.prisma.payerNegotiation.update({
      where: { negotiationId },
      data: updateData,
      include: { partyA_PaymentReceivingInfo: true, partyB_PaymentReceivingInfo: true }
    });    // Emit only to user rooms to avoid duplicates
    this.io.to(negotiation.partyA_Id).emit(PAYMENT_INFO_SUBMITTED, {
      negotiationId,
      userId,
      status: isPartyA ? updatedPartyAStatus : updatedPartyBStatus,
    });
    this.io.to(negotiation.partyB_Id).emit(PAYMENT_INFO_SUBMITTED, {
      negotiationId,
      userId,
      status: isPartyA ? updatedPartyAStatus : updatedPartyBStatus,
    });
    const payload = this.mapNegotiationToPayload(updatedNegotiation);
    // Emit only to user rooms to avoid duplicates
    this.io.to(negotiation.partyA_Id).emit(NEGOTIATION_STATE_UPDATED, payload);
    this.io.to(negotiation.partyB_Id).emit(NEGOTIATION_STATE_UPDATED, payload);
    this.logger.info(`[PayerNegotiationService] Submitted receiving info for user ${userId} in negotiation ${negotiationId}. New status: ${newNegotiationStatus}`);
    return payload;
  }

  async proposeFirstPayer(
    negotiationId: string,
    proposerId: string,
    proposedPayerId: string,
    proposalMessage?: string
  ): Promise<PayerNegotiationStatePayload> {
    const negotiation = await this.prisma.payerNegotiation.findUnique({
      where: { negotiationId },
      include: {
        transaction: {
          select: {
            chatSessionId: true,
            id: true
          }
        },
        partyA_PaymentReceivingInfo: true,
        partyB_PaymentReceivingInfo: true
      }
    });

    if (!negotiation) {
        this.logger.error(`[PayerNegotiationService] Negotiation not found: ${negotiationId} in proposeFirstPayer`);
        throw new Error('Negotiation not found');
    }
    if (!negotiation.transaction) {
        this.logger.error(`[PayerNegotiationService] Transaction not found for negotiation ${negotiationId}`);
        throw new Error('Transaction not found for negotiation');
    }

    if (proposerId !== negotiation.partyA_Id && proposerId !== negotiation.partyB_Id) {
      this.logger.warn(`[PayerNegotiationService] Proposer ${proposerId} not part of negotiation ${negotiationId}`);
      throw new Error('Proposer is not part of this negotiation');
    }
    if (proposedPayerId !== negotiation.partyA_Id && proposedPayerId !== negotiation.partyB_Id) {
      this.logger.warn(`[PayerNegotiationService] Proposed payer ${proposedPayerId} not part of negotiation ${negotiationId}`);
      throw new Error('Proposed payer is not part of this negotiation');
    }

    const proposerIsPartyA = proposerId === negotiation.partyA_Id;
    const proposerReceivingInfoStatus = proposerIsPartyA
      ? negotiation.partyA_receivingInfoStatus
      : negotiation.partyB_receivingInfoStatus;    if (proposerReceivingInfoStatus === ReceivingInfoStatus.PENDING_INPUT) {
      this.logger.warn(`[PayerNegotiationService] User ${proposerId} must provide payment info before proposing in negotiation ${negotiationId}`);
      throw new Error('User must provide their payment receiving information before making a proposal.');
    }

    // Check if negotiation is already finalized
    if (negotiation.negotiationStatus === NegotiationStatus.FINALIZED) {
      this.logger.warn(`[PayerNegotiationService] Cannot make proposal for finalized negotiation ${negotiationId}`);
      throw new Error('Cannot make a proposal at this time');
    }    const canProposeNow = 
      negotiation.negotiationStatus === NegotiationStatus.PENDING_RESPONSE ||
      negotiation.negotiationStatus === NegotiationStatus.READY_TO_NEGOTIATE ||
      (negotiation.currentProposal_ById === 'system');

    if (!canProposeNow) {
      this.logger.warn(`[PayerNegotiationService] Cannot make proposal for negotiation ${negotiationId}. Current status: ${negotiation.negotiationStatus}`);
      throw new Error('Cannot make a proposal at this time. Ensure your payment info is submitted and the negotiation is in a state to accept proposals.');
    }

    // Determine if this proposal should be marked as final offer
    // A proposal is final if:
    // 1. There was already a user proposal (not system recommendation)
    // 2. This is a counter-proposal to that user proposal
    // 3. The previous proposal was not by the same user
    const shouldMarkAsFinal = 
      negotiation.currentProposal_ById && 
      negotiation.currentProposal_ById !== 'system' &&
      negotiation.currentProposal_ById !== proposerId;

    if (shouldMarkAsFinal) {
      this.logger.info(`[PayerNegotiationService] Marking proposal as final offer for negotiation ${negotiationId}`);
    }

    const updatedNegotiation = await this.prisma.payerNegotiation.update({
      where: { negotiationId },
      data: {
        currentProposal_PayerId: proposedPayerId,
        currentProposal_ById: proposerId,
        currentProposal_Message: proposalMessage ?? null,
        partyA_agreedToCurrentProposal: proposerIsPartyA ? true : false,
        partyB_agreedToCurrentProposal: proposerIsPartyA ? false : true,
        negotiationStatus: NegotiationStatus.PENDING_RESPONSE,
        isFinalOffer: shouldMarkAsFinal,
      },      include: { 
        partyA_PaymentReceivingInfo: true, 
        partyB_PaymentReceivingInfo: true,
        transaction: { 
          select: { 
            chatSessionId: true, 
            id: true 
          } 
        } 
      }
    });

    // Check if both parties now agree to the current proposal (auto-finalization)
    const bothPartiesAgree = updatedNegotiation.partyA_agreedToCurrentProposal && updatedNegotiation.partyB_agreedToCurrentProposal;
    
    if (bothPartiesAgree) {
      this.logger.info(`[PayerNegotiationService] Both parties agreed to proposal. Auto-finalizing negotiation ${negotiationId} with payer: ${proposedPayerId}`);
      
      // Finalize the negotiation immediately
      const finalizedNegotiation = await this.prisma.payerNegotiation.update({
        where: { negotiationId },
        data: {
          finalizedPayerId: proposedPayerId,
          negotiationStatus: NegotiationStatus.FINALIZED,
          paymentTimerDueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
        },
        include: { 
          partyA_PaymentReceivingInfo: true, 
          partyB_PaymentReceivingInfo: true,
          transaction: { 
            select: { 
              chatSessionId: true, 
              id: true 
            } 
          } 
        }
      });      // Update transaction status and emit finalization events
      await this.transactionService.designateFirstPayer(negotiation.transactionId, proposerId, proposedPayerId);
      
      // Create auto-finalization notifications for both parties
      const finalizedUser = await this.prisma.user.findUnique({
        where: { id: proposedPayerId },
        select: { username: true, email: true }      });
      const finalizedPayerName = finalizedUser?.username || finalizedUser?.email || `User ${proposedPayerId.substring(0,6)}`;
      
      await this.notificationService.createNotification({
        userId: negotiation.partyA_Id,
        type: 'TRANSACTION_UPDATE' as NotificationType,
        message: `Payer Decision Finalized: The payer decision has been automatically finalized. ${finalizedPayerName} will pay first in your transaction.`,
        relatedEntityType: 'TRANSACTION',
        relatedEntityId: negotiation.transactionId,
        data: JSON.stringify({
          negotiationId: negotiationId,
          finalizedPayerId: proposedPayerId
        })
      });
      
      await this.notificationService.createNotification({
        userId: negotiation.partyB_Id,
        type: 'TRANSACTION_UPDATE' as NotificationType,
        message: `Payer Decision Finalized: The payer decision has been automatically finalized. ${finalizedPayerName} will pay first in your transaction.`,
        relatedEntityType: 'TRANSACTION',
        relatedEntityId: negotiation.transactionId,        data: JSON.stringify({
          negotiationId: negotiationId,
          finalizedPayerId: proposedPayerId
        })
      });
      
      const finalPayloadNegotiation = {
        ...finalizedNegotiation,
        transaction: undefined
      };
      
      return this.mapNegotiationToPayload(finalPayloadNegotiation);    }

    // If not both agreed, emit normal proposal events
    
    // Create notification for the other party about the new proposal
    const otherPartyId = proposerId === negotiation.partyA_Id ? negotiation.partyB_Id : negotiation.partyA_Id;
    const proposerUser = await this.prisma.user.findUnique({ 
      where: { id: proposerId }, 
      select: { username: true, email: true }    });
    const proposerDisplayName = proposerUser?.username || proposerUser?.email || `User ${proposerId.substring(0,6)}`;
    
    await this.notificationService.createNotification({
      userId: otherPartyId,
      type: 'TRANSACTION_ACTION_REQUIRED' as NotificationType,
      message: `New Payer Proposal: ${proposerDisplayName} proposed who should pay first in your transaction. Check and respond to the proposal.`,
      relatedEntityType: 'TRANSACTION',
      relatedEntityId: negotiation.transactionId,
      data: JSON.stringify({
        negotiationId: negotiationId,
        proposerId: proposerId,
        proposedPayerId: proposedPayerId,
        proposalMessage: proposalMessage
      })
    });

    // Emit only to user rooms to avoid duplicates
    this.io.to(negotiation.partyA_Id).emit(PROPOSAL_MADE, {
      negotiationId,
      proposerId,
      proposedPayerId,
      message: proposalMessage,
    });
    this.io.to(negotiation.partyB_Id).emit(PROPOSAL_MADE, {
      negotiationId,
      proposerId,
      proposedPayerId,
      message: proposalMessage,
    });

    if (proposalMessage && updatedNegotiation.transaction?.chatSessionId) {
      const proposerUser = await this.prisma.user.findUnique({ where: { id: proposerId }, select: { username: true, email: true } });
      const proposerDisplayName = proposerUser?.username || proposerUser?.email || `User ${proposerId.substring(0,6)}`;

      const proposalMessageData = {
        username: proposerDisplayName,
        message: proposalMessage
      };

      await this.chatService.addSystemMessageToChat(
        updatedNegotiation.transaction.chatSessionId,
        'systemMessages.proposal.from',
        updatedNegotiation.transactionId,
        proposalMessageData
      );
    }

    // Create a minimal negotiation object for mapNegotiationToPayload since it doesn't need full transaction data
    const payloadNegotiation = {
      ...updatedNegotiation,
      transaction: undefined // Remove transaction to avoid type conflicts
    };

    const payload = this.mapNegotiationToPayload(payloadNegotiation);
    // Emit only to user rooms to avoid duplicates
    this.io.to(negotiation.partyA_Id).emit(NEGOTIATION_STATE_UPDATED, payload);
    this.io.to(negotiation.partyB_Id).emit(NEGOTIATION_STATE_UPDATED, payload);
    this.logger.info(`[PayerNegotiationService] Proposal made by ${proposerId} for payer ${proposedPayerId} in negotiation ${negotiationId}`);
    return payload;
  }

  async agreeToProposal(negotiationId: string, userId: string): Promise<PayerNegotiationStatePayload> {
    this.logger.info(`[PayerNegotiationService] agreeToProposal called for negotiationId: ${negotiationId}, userId: ${userId}`);
    
    if (!this.chatService) {
      this.logger.error("[PayerNegotiationService] CRITICAL: ChatService is undefined in agreeToProposal. This should not happen with constructor injection.");
      throw new Error("ChatService is not available in PayerNegotiationService (was undefined in agreeToProposal).");
    }
    if (!this.transactionService) {
        this.logger.error("[PayerNegotiationService] CRITICAL: TransactionService is undefined in agreeToProposal.");
        throw new Error("TransactionService is not available in PayerNegotiationService.");
    }

    const negotiation = await this.prisma.payerNegotiation.findUnique({
      where: { negotiationId },
      include: {
        transaction: {
          include: {
            currencyAProvider: { select: { id: true, username: true } },
            currencyBProvider: { select: { id: true, username: true } },
          }
        },
        partyA_PaymentReceivingInfo: true,
        partyB_PaymentReceivingInfo: true,
      },
    });

    if (!negotiation) {
      this.logger.error(`[PayerNegotiationService] Negotiation not found: ${negotiationId} in agreeToProposal`);
      throw new Error('Negotiation not found');
    }
    if (!negotiation.transaction) {
      this.logger.error(`[PayerNegotiationService] Transaction data not found for negotiation ${negotiationId}`);
      throw new Error('Transaction data not found for negotiation');
    }    if (!negotiation.transaction.chatSessionId) {
      this.logger.error(`[PayerNegotiationService] agreeToProposal: chatSessionId is missing for transaction ${negotiation.transactionId}`);
      throw new Error('Chat session ID is missing for the transaction.');
    }
    
    // Check if user is part of the negotiation
    if (userId !== negotiation.partyA_Id && userId !== negotiation.partyB_Id) {
      this.logger.warn(`[PayerNegotiationService] User ${userId} is not part of negotiation ${negotiationId}`);
      throw new Error('User is not part of this negotiation');
    }
    
    const chatSessionId = negotiation.transaction.chatSessionId;

    let updatedNegotiationData = negotiation;

    const isPartyA = negotiation.partyA_Id === userId;
    let newPartyAAgreed = negotiation.partyA_agreedToCurrentProposal;
    let newPartyBAgreed = negotiation.partyB_agreedToCurrentProposal;

    if (isPartyA) {
        newPartyAAgreed = true;
    } else {
        newPartyBAgreed = true;
    }
    
    let newNegotiationStatus = negotiation.negotiationStatus;
    let finalizedPayerId = negotiation.finalizedPayerId;    // First, update the negotiation data and send agreement message
    updatedNegotiationData = await this.prisma.payerNegotiation.update({
      where: { negotiationId },
      data: {
        partyA_agreedToCurrentProposal: newPartyAAgreed,
        partyB_agreedToCurrentProposal: newPartyBAgreed,
        negotiationStatus: newNegotiationStatus,
        finalizedPayerId: finalizedPayerId,
      },
      include: {
        transaction: {
          include: {
            currencyAProvider: { select: { id: true, username: true } },
            currencyBProvider: { select: { id: true, username: true } },
          }
        },
        partyA_PaymentReceivingInfo: true,
        partyB_PaymentReceivingInfo: true,
      },
    });
    
    const agreedPartyUsername = getUsernameFromTransactionContext(updatedNegotiationData.transaction, userId);

    const agreementMessageData = {
      username: agreedPartyUsername
    };

    this.logger.info(`[PayerNegotiationService] agreeToProposal: Attempting to call this.chatService.addSystemMessageToChat for chatSessionId: ${chatSessionId}`);
    await this.chatService.addSystemMessageToChat(
      chatSessionId,
      'systemMessages.proposal.agreed',
      negotiation.transactionId,
      agreementMessageData
    );
    this.logger.info(`[PayerNegotiationService] agreeToProposal: System message sent successfully for negotiation ${negotiationId}.`);    // Create notification for the other party about the agreement
    const otherPartyId = userId === negotiation.partyA_Id ? negotiation.partyB_Id : negotiation.partyA_Id;
    
    await this.notificationService.createNotification({
      userId: otherPartyId,
      type: 'TRANSACTION_UPDATE' as NotificationType,
      message: `Proposal Agreement: ${agreedPartyUsername} agreed to the payer proposal in your transaction.`,
      relatedEntityType: 'TRANSACTION',
      relatedEntityId: negotiation.transactionId,
      data: JSON.stringify({
        negotiationId: negotiationId,
        agreedUserId: userId
      })
    });

    // Now check if both parties agreed and finalize if so
    if (newPartyAAgreed && newPartyBAgreed) {
        newNegotiationStatus = NegotiationStatus.FINALIZED;
        finalizedPayerId = negotiation.currentProposal_PayerId;

        if (!finalizedPayerId) {
            this.logger.error(`[PayerNegotiationService] Cannot finalize negotiation ${negotiationId}: currentProposal_PayerId is null.`);
            throw new Error('Cannot finalize negotiation: proposed payer ID is missing.');
        }

        // Update negotiation status to FINALIZED
        updatedNegotiationData = await this.prisma.payerNegotiation.update({
          where: { negotiationId },
          data: {
            negotiationStatus: newNegotiationStatus,
            finalizedPayerId: finalizedPayerId,
          },
          include: {
            transaction: {
              include: {
                currencyAProvider: { select: { id: true, username: true } },
                currencyBProvider: { select: { id: true, username: true } },
              }
            },
            partyA_PaymentReceivingInfo: true,
            partyB_PaymentReceivingInfo: true,
          },
        });        // Call TransactionService.designateFirstPayer (this will send the proper finalization message)
        this.logger.info(`[PayerNegotiationService] Both parties agreed. Finalized Payer ID: ${finalizedPayerId}. Calling designateFirstPayer for TX: ${negotiation.transactionId}`);
        await this.transactionService.designateFirstPayer(negotiation.transactionId, userId, finalizedPayerId);
        this.logger.info(`[PayerNegotiationService] Transaction ${negotiation.transactionId} status updated by designateFirstPayer. Negotiation finalized.`);

        // Create finalization notifications for both parties
        const finalizedUser = await this.prisma.user.findUnique({
          where: { id: finalizedPayerId },
          select: { username: true, email: true }        });
        const finalizedPayerName = finalizedUser?.username || finalizedUser?.email || `User ${finalizedPayerId.substring(0,6)}`;
        
        await this.notificationService.createNotification({
          userId: negotiation.partyA_Id,
          type: 'TRANSACTION_UPDATE' as NotificationType,
          message: `Payer Decision Finalized: The payer decision has been finalized. ${finalizedPayerName} will pay first in your transaction.`,
          relatedEntityType: 'TRANSACTION',
          relatedEntityId: negotiation.transactionId,
          data: JSON.stringify({
            negotiationId: negotiationId,
            finalizedPayerId: finalizedPayerId
          })
        });

        await this.notificationService.createNotification({
          userId: negotiation.partyB_Id,
          type: 'TRANSACTION_UPDATE' as NotificationType,
          message: `Payer Decision Finalized: The payer decision has been finalized. ${finalizedPayerName} will pay first in your transaction.`,
          relatedEntityType: 'TRANSACTION',
          relatedEntityId: negotiation.transactionId,
          data: JSON.stringify({
            negotiationId: negotiationId,
            finalizedPayerId: finalizedPayerId          })
        });
    }
    
    const payload = this.mapNegotiationToPayload(updatedNegotiationData);
    // Emit only to user rooms to avoid duplicates (users may be in both transaction and user rooms)
    this.io.to(negotiation.partyA_Id).emit(NEGOTIATION_STATE_UPDATED, payload);
    this.io.to(negotiation.partyB_Id).emit(NEGOTIATION_STATE_UPDATED, payload);

    return payload;
  }

  async cancelNegotiation(negotiationId: string, reason: string): Promise<PayerNegotiationStatePayload> {
    this.logger.info(`[PayerNegotiationService] Canceling negotiation ${negotiationId}. Reason: ${reason}`);
    
    const negotiation = await this.prisma.payerNegotiation.findUnique({
      where: { negotiationId },
      include: {
        transaction: {
          select: {
            id: true,
            status: true
          }
        },
        partyA_PaymentReceivingInfo: true,
        partyB_PaymentReceivingInfo: true,
      },
    });

    if (!negotiation) {
      this.logger.error(`[PayerNegotiationService] Negotiation not found: ${negotiationId} in cancelNegotiation`);
      throw new Error('Negotiation not found');
    }

    if (negotiation.negotiationStatus === NegotiationStatus.FINALIZED) {
      this.logger.warn(`[PayerNegotiationService] Cannot cancel finalized negotiation ${negotiationId}`);
      throw new Error('Cannot cancel a finalized negotiation');
    }

    if (negotiation.negotiationStatus === NegotiationStatus.CANCELLED) {
      this.logger.warn(`[PayerNegotiationService] Negotiation ${negotiationId} is already cancelled`);
      throw new Error('Negotiation is already cancelled');
    }

    // Update negotiation status to cancelled
    const updatedNegotiation = await this.prisma.payerNegotiation.update({
      where: { negotiationId },
      data: {
        negotiationStatus: NegotiationStatus.CANCELLED,
      },
      include: {
        partyA_PaymentReceivingInfo: true,
        partyB_PaymentReceivingInfo: true,
      },
    });

    // Update transaction status to cancelled as well
    if (negotiation.transaction) {
      await this.transactionService.cancelTransaction(negotiation.transaction.id, reason);
    }

    // Create notifications for both parties
    await this.notificationService.createNotification({
      userId: negotiation.partyA_Id,
      type: 'TRANSACTION_UPDATE' as NotificationType,
      message: `Transaction Cancelled: The negotiation has been cancelled. Reason: ${reason}`,
      relatedEntityType: 'TRANSACTION',
      relatedEntityId: negotiation.transactionId,
      data: JSON.stringify({
        negotiationId: negotiationId,
        reason: reason
      })
    });

    await this.notificationService.createNotification({
      userId: negotiation.partyB_Id,
      type: 'TRANSACTION_UPDATE' as NotificationType,
      message: `Transaction Cancelled: The negotiation has been cancelled. Reason: ${reason}`,
      relatedEntityType: 'TRANSACTION',
      relatedEntityId: negotiation.transactionId,
      data: JSON.stringify({
        negotiationId: negotiationId,
        reason: reason
      })
    });

    const payload = this.mapNegotiationToPayload(updatedNegotiation);
    
    // Emit cancellation events to both parties
    this.io.to(negotiation.partyA_Id).emit(NEGOTIATION_STATE_UPDATED, payload);
    this.io.to(negotiation.partyB_Id).emit(NEGOTIATION_STATE_UPDATED, payload);

    this.logger.info(`[PayerNegotiationService] Negotiation ${negotiationId} cancelled successfully`);
    return payload;
  }

  async getNegotiation(negotiationId: string) {
    return this.prisma.payerNegotiation.findUnique({
      where: { negotiationId }
    });
  }
}

// Remove the singleton export - the instance will be created in index.ts
// export const payerNegotiationService = new PayerNegotiationService();
