import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { TransactionStatusEnum } from '@/types/transaction';
import { useAuthStore } from '@/stores/auth';
import { usePaymentMethodsStore } from '@/stores/paymentMethodsStore';
import { transactionalChatApiService, type TransactionChatDetails as ApiTransactionChatDetails, type TransactionFeedItem } from '@/services/transactionalChatApi';
import centralizedSocketManager from '@/services/centralizedSocketManager';
import { debugLogger } from '@/utils/debugLogger';

// Interface for feed items - using the API types
export interface FeedItem extends TransactionFeedItem {}

// Interface for other user details
export interface OtherUser {
  id: string;
  name: string;
  profilePic?: string;
  reputation: number;
}

// Type alias for the nested transactionDetails from the API
export type TransactionChatDetails = ApiTransactionChatDetails['transactionDetails'];

// Interface for timer state
export interface TimerState {
  isActive: boolean;
  remainingSeconds: number;
}

// Interface for error state
export interface ErrorState {
  hasError: boolean;
  message: string;
}

// Interface for pinned action state
export interface PinnedAction {
  title: string;
  cardId: string;
  actionType: string;
  stepIndex: number;
}

// Step configuration interface
export interface StepConfig {
  id: number;
  key: string;
  titleKey: string;
  isUserAction: boolean;
  status: TransactionStatusEnum;
}

export const useTransactionalChatStore = defineStore('transactionalChat', () => {
  // State
  const otherUser = ref<OtherUser | null>(null);
  const transactionDetails = ref<TransactionChatDetails | null>(null);
  const transactionStatus = ref<string | null>(null); // Add transaction status
  const currentTransactionIdRef = ref<string | null>(null); // Add transaction ID
  const currentStepIndex = ref<number>(0);
  const feedItems = ref<FeedItem[]>([]);
  const timer = ref<TimerState>({ isActive: false, remainingSeconds: 0 });
  const errorState = ref<ErrorState>({ hasError: false, message: '' });
  const pinnedAction = ref<PinnedAction | null>(null);
  const isActionCardVisible = ref<boolean>(true); // Track if action card is in viewport
  const chatSessionId = ref<string>('');
  const isLoading = ref<boolean>(false);
  const showPaymentDetailsForm = ref<boolean>(false);
  // Shrink status bar state
  const isStatusBarShrunk = ref<boolean>(false);
  
  // Computed property for showing banner only when action card is out of view
  const shouldShowPinnedBanner = computed(() => {
    return pinnedAction.value !== null && !isActionCardVisible.value;
  });

  // Timer interval reference
  let timerInterval: NodeJS.Timeout | null = null;
  
  // Debounce timeout for fetchTransaction
  let fetchTransactionDebounceTimeout: NodeJS.Timeout | null = null;
  
  // Socket listener cleanup functions
  let socketUnsubscribeFunctions: (() => void)[] = [];
  
  // Unique ID counter to avoid duplicate keys
  let idCounter = 0;
  const generateUniqueId = (prefix: string) => {
    return `${prefix}-${Date.now()}-${++idCounter}`;
  };

  // Steps that require a timer
  const stepsWithTimer = ['makePayment', 'confirmReceipt', 'makeSecondPayment', 'confirmFirstPaymentReceipt'];

  // Internal state tracking
  const currentTransactionId = ref<string>('');
  
  // Helper function to get current transaction ID
  const getCurrentTransactionId = () => {
    return currentTransactionId.value;
  };
  
  // Socket cleanup helper
  const clearSocketListeners = () => {
    console.log('🧹 [TransactionalChatStore] Clearing socket listeners');
    socketUnsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    socketUnsubscribeFunctions = [];
  };
  
  // Socket event handlers
  const setupSocketListeners = (transactionId: string, chatSessionId: string) => {
    // Clear any existing listeners to prevent data leakage
    clearSocketListeners();
    
    currentTransactionId.value = transactionId;
    
    // Listen for transaction status updates
    const transactionUnsubscribe = centralizedSocketManager.on('TRANSACTION_STATUS_UPDATED', (payload: any) => {
      // Validate payload structure before accessing properties
      if (payload && typeof payload === 'object' && 
          typeof payload.transactionId === 'string' && 
          payload.transactionId === transactionId) {
        console.log('📡 Received transaction status update:', payload);
        // Use debounced fetch with shorter delay and force option for socket events
        debouncedFetchTransaction(transactionId, 100, true);
      } else {
        console.log('📡 Ignoring transaction status update for different transaction:', payload?.transactionId, 'vs current:', transactionId);
      }
    });
    socketUnsubscribeFunctions.push(transactionUnsubscribe);
    
    // Listen for chat messages
    const chatUnsubscribe = centralizedSocketManager.on('CHAT_MESSAGE_RECEIVE', (payload: any) => {
      // Validate payload structure before accessing properties
      if (payload && typeof payload === 'object' && 
          typeof payload.chatSessionId === 'string' &&
          payload.chatSessionId === chatSessionId) {
        console.log('📡 Received chat message:', payload);
        
        // Add message to feed if not already present
        const existingMessage = feedItems.value.find(item => item.id === payload.messageId);
        if (!existingMessage) {
          const newMessage: FeedItem = {
            id: payload.messageId,
            type: 'chat',
            timestamp: payload.createdAt,
            content: payload.content,
            sender: {
              id: payload.sender.id,
              name: payload.sender.username,
              isCurrentUser: payload.sender.id === useAuthStore().user?.id
            }
          };
          feedItems.value.push(newMessage);
        }
      } else {
        console.log('📡 Ignoring chat message for different session:', payload?.chatSessionId, 'vs current:', chatSessionId);
      }
    });
    socketUnsubscribeFunctions.push(chatUnsubscribe);
    
    // Listen for system messages
    const systemUnsubscribe = centralizedSocketManager.on('SYSTEM_MESSAGE_RECEIVE', (payload: any) => {
      // Validate payload structure before accessing properties
      if (payload && typeof payload === 'object' && 
          typeof payload.chatSessionId === 'string' &&
          payload.chatSessionId === chatSessionId) {
        console.log('📡 Received system message:', payload);
        
        // Add system log to feed if not already present
        const existingLog = feedItems.value.find(item => item.id === payload.messageId);
        if (!existingLog) {
          const systemLog: FeedItem = {
            id: payload.messageId,
            type: 'systemLog',
            timestamp: payload.createdAt,
            message: payload.content
          };
          feedItems.value.push(systemLog);
        }
      } else {
        console.log('📡 Ignoring system message for different session:', payload?.chatSessionId, 'vs current:', chatSessionId);
      }
    });
    socketUnsubscribeFunctions.push(systemUnsubscribe);
  };

  // Step definitions - 7 steps as per the brief
  const steps: StepConfig[] = [
    {
      id: 1,
      key: 'paymentInfo',
      titleKey: 'transactionalChat.steps.paymentInfo',
      isUserAction: true,
      status: TransactionStatusEnum.PENDING_AGREEMENT
    },
    {
      id: 2,
      key: 'negotiation',
      titleKey: 'transactionalChat.steps.negotiation',
      isUserAction: true,
      status: TransactionStatusEnum.AWAITING_FIRST_PAYER_DESIGNATION
    },
    {
      id: 3,
      key: 'makePayment',
      titleKey: 'transactionalChat.steps.makePayment',
      isUserAction: true,
      status: TransactionStatusEnum.AWAITING_FIRST_PAYER_PAYMENT
    },
    {
      id: 4,
      key: 'confirmReceipt',
      titleKey: 'transactionalChat.steps.confirmReceipt',
      isUserAction: true,
      status: TransactionStatusEnum.AWAITING_SECOND_PAYER_CONFIRMATION
    },
    {
      id: 5,
      key: 'makeSecondPayment',
      titleKey: 'transactionalChat.steps.makeSecondPayment',
      isUserAction: true,
      status: TransactionStatusEnum.AWAITING_SECOND_PAYER_PAYMENT
    },
    {
      id: 6,
      key: 'confirmFirstPaymentReceipt',
      titleKey: 'transactionalChat.steps.confirmFirstPaymentReceipt',
      isUserAction: true,
      status: TransactionStatusEnum.AWAITING_FIRST_PAYER_CONFIRMATION
    },
    {
      id: 7,
      key: 'finalized',
      titleKey: 'transactionalChat.steps.finalized',
      isUserAction: false,
      status: TransactionStatusEnum.COMPLETED
    }
  ];

  // Getters
  const currentStep = computed(() => {
    return steps[currentStepIndex.value] || steps[0];
  });

  const isUsersTurn = computed(() => {
    const authStore = useAuthStore();
    if (!authStore.user?.id || !transactionDetails.value) return false;
    
    const step = currentStep.value;
    if (!step) return false;

    // Check if it's a user action step
    if (!step.isUserAction) return false;

    // Additional logic based on step and user role
    switch (step.key) {
      case 'paymentInfo':
        return true; // Always user's turn to provide payment info
      case 'negotiation':
        return true; // Always user's turn to negotiate
      case 'makePayment':
        return transactionDetails.value.isUserFirstPayer; // User is first payer, needs to make payment
      case 'confirmReceipt':
        return !transactionDetails.value.isUserFirstPayer; // User is second payer, needs to confirm receipt of first payment
      case 'makeSecondPayment':
        return !transactionDetails.value.isUserFirstPayer; // User is second payer, needs to make second payment
      case 'confirmFirstPaymentReceipt':
        return transactionDetails.value.isUserFirstPayer; // User is first payer, needs to confirm receipt of second payment
      default:
        return false;
    }
  });

  const activeActionCard = computed(() => {
    return feedItems.value.find(item => 
      item.type === 'actionCard' && 
      item.actionType === currentStep.value?.key
    ) || null;
  });

  const totalSteps = computed(() => steps.length);

  // Actions
  const fetchTransaction = async (transactionId: string, force: boolean = false) => {
    console.log('🔄 Fetching transaction:', transactionId, force ? '(forced)' : '');
    
    // Prevent multiple concurrent calls unless forced
    if (isLoading.value && !force) {
      console.log('⚠️ Already loading transaction, skipping...');
      return;
    }
    
    isLoading.value = true;
    errorState.value = { hasError: false, message: '' };
    
    try {
      // Call the real API
      const transactionData = await transactionalChatApiService.fetchTransactionChatDetails(transactionId);
      
      console.log('🔍 [fetchTransaction] API Response - currentStepIndex:', transactionData.currentStepIndex);
      console.log('🔍 [fetchTransaction] API Response - feedItems count:', transactionData.feedItems.length);
      
      // Update store state with API data
      otherUser.value = {
        ...transactionData.otherUser,
        profilePic: transactionData.otherUser.profilePic || undefined
      };
      console.log('🔍 [fetchTransaction] otherUser after assignment:', otherUser.value);
      transactionDetails.value = transactionData.transactionDetails;
      console.log('🔍 [fetchTransaction] transactionDetails.value after assignment:', JSON.parse(JSON.stringify(transactionDetails.value)));
      console.log('🔍 [fetchTransaction] transactionDetails after assignment:', transactionDetails.value);
      // Store transaction status and ID for confetti triggering
      transactionStatus.value = transactionData.status;
      currentTransactionIdRef.value = transactionData.id;
      console.log('🔍 [fetchTransaction] Transaction status:', transactionStatus.value, 'ID:', currentTransactionIdRef.value);
      currentStepIndex.value = transactionData.currentStepIndex;
      feedItems.value = transactionData.feedItems;
      console.log('🔍 [fetchTransaction] feedItems after API response:', feedItems.value);
      chatSessionId.value = transactionData.chatSessionId;
      
      // Set timer if active - but first clear any existing timer to prevent data leakage
      stopTimer(); // Always stop any existing timer first

      if (transactionData.timer && transactionData.timer.isActive) {
        console.log('🔍 [fetchTransaction] Setting timer for transaction:', transactionData.id, 'seconds:', transactionData.timer.remainingSeconds);
        timer.value = transactionData.timer;
        startTimer(transactionData.timer.remainingSeconds);
      } else {
        console.log('🔍 [fetchTransaction] No active timer for transaction:', transactionData.id);
        // Try to fetch timer separately for time-sensitive steps
        const currentStep = steps[currentStepIndex.value];
        if (currentStep && stepsWithTimer.includes(currentStep.key)) {
          console.log('🔍 [fetchTransaction] Step requires timer, fetching timer data separately...');
          try {
            const timerData = await transactionalChatApiService.getTimerStatus(transactionId);
            if (timerData.isActive) {
              console.log('🔍 [fetchTransaction] Found active timer via separate API call:', timerData.remainingSeconds);
              timer.value = timerData;
              startTimer(timerData.remainingSeconds);
            } else {
              timer.value = { isActive: false, remainingSeconds: 0 };
            }
          } catch (error) {
            console.log('🔍 [fetchTransaction] Failed to fetch timer separately:', error);
            timer.value = { isActive: false, remainingSeconds: 0 };
          }
        } else {
          timer.value = { isActive: false, remainingSeconds: 0 };
        }
      }
      
      // Set pinned action based on current step
      const currentStep = steps[currentStepIndex.value];
      console.log('🔍 [fetchTransaction] Current Step Key:', currentStep?.key);
      console.log("🔍 [fetchTransaction] Is User's Turn:", isUsersTurn.value);
      
      if (currentStep) {
        let actionCard = feedItems.value.find(item => 
          item.type === 'actionCard' && 
          item.actionType === currentStep.key
        );
        console.log('🔍 [fetchTransaction] Existing actionCard for current step:', actionCard);

        // Action card creation is now handled by the backend
        // The backend will create action cards based on the current step and user turn
        console.log('🔍 [fetchTransaction] Action card for current step:', currentStep?.key, 'should be handled by backend');
        
        // Set pinned action based on current step and user turn
        if (currentStep && isUsersTurn.value) {
          let actionCard = feedItems.value.find(item => 
            item.type === 'actionCard' && 
            item.actionType === currentStep.key
          );

          if (actionCard) {
            setPinnedAction(actionCard.actionType!, actionCard.id);
          } else if (currentStep.key === 'confirmReceipt' || currentStep.key === 'makeSecondPayment' || currentStep.key === 'confirmFirstPaymentReceipt') {
            // For dynamic action bar steps
            setPinnedAction(currentStep.key, `dynamic-action-${currentStep.key}`);
          }
        }
      }
      
      // Set up socket listeners for real-time updates
      setupSocketListeners(transactionData.id, transactionData.chatSessionId);
      
      console.log('✅ Transaction fetched successfully, final feed items count:', feedItems.value.length);
    } catch (error: any) {
      console.error('❌ Error fetching transaction:', error);
      errorState.value = {
        hasError: true,
        message: error.message || 'Failed to fetch transaction'
      };
    } finally {
      isLoading.value = false;
    }
  };

  // Debounced version to prevent excessive API calls
  const debouncedFetchTransaction = (transactionId: string, delay: number = 200, force: boolean = false) => {
    // Clear any existing timeout
    if (fetchTransactionDebounceTimeout) {
      clearTimeout(fetchTransactionDebounceTimeout);
    }
    
    // Set new timeout
    fetchTransactionDebounceTimeout = setTimeout(() => {
      fetchTransaction(transactionId, force);
    }, delay);
  };

  const performAction = async (actionType: string, data?: any) => {
    isLoading.value = true;
    errorState.value = { hasError: false, message: '' };

    try {
      // Get current transaction ID from chat session or other source
      const currentTransactionId = getCurrentTransactionId();
      if (!currentTransactionId) {
        throw new Error('No active transaction found');
      }
      
      // Special handling for paymentInfo action - create action card with payment methods
      if (actionType === 'paymentInfo') {
        // Import composable dynamically to avoid circular dependencies
        const { usePaymentMethodsIntegration } = await import('@/composables/usePaymentMethodsIntegration');
        
        const paymentIntegration = usePaymentMethodsIntegration();
        const paymentMethodsStore = usePaymentMethodsStore();
        
        // Determine the currency the user needs payment details for
        // User needs payment details for the currency they're RECEIVING (currencyTo)
        let currency = 'IRR'; // Default fallback
        
        if (transactionDetails.value) {
          currency = transactionDetails.value.currencyTo;
        }
        
        // Debug logs only in development environment
        debugLogger.log('🔍 [TransactionalChat] Payment info action triggered');
        debugLogger.log('🔍 [TransactionalChat] Transaction details:', {
          currencyFrom: transactionDetails.value?.currencyFrom,
          currencyTo: transactionDetails.value?.currencyTo,
          amountToSend: transactionDetails.value?.amountToSend,
          amountToReceive: transactionDetails.value?.amountToReceive,
          isUserFirstPayer: transactionDetails.value?.isUserFirstPayer
        });
        debugLogger.log('🔍 [TransactionalChat] Selected currency for payment methods:', currency);
        debugLogger.log('🔍 [TransactionalChat] This means user will RECEIVE', currency, 'and needs payment details for', currency);
        
        // Fetch payment methods for the specific currency
        let currencyMethods: any[] = [];
        try {
          debugLogger.log('🔍 [TransactionalChat] BEFORE fetchMethodsByCurrency - Store contains:', paymentMethodsStore.paymentMethods.length, 'methods');
          await paymentMethodsStore.fetchMethodsByCurrency(currency);
          debugLogger.log('🔍 [TransactionalChat] AFTER fetchMethodsByCurrency - Store contains:', paymentMethodsStore.paymentMethods.length, 'methods');
          
          currencyMethods = paymentMethodsStore.getMethodsForTransaction(currency);
          debugLogger.log('🔍 [TransactionalChat] Fetched payment methods for currency:', currency, 'count:', currencyMethods.length);
          debugLogger.log('🔍 [TransactionalChat] Currency-specific payment methods:', currencyMethods.map(m => ({id: m.id, currency: m.currency, bank: m.bankName})));
        } catch (error) {
          debugLogger.log('⚠️ [TransactionalChat] Failed to fetch payment methods:', error);
          // Fallback to existing methods if fetch fails
          currencyMethods = paymentMethodsStore.getMethodsForTransaction(currency);
        }
        
        // Create action card with currency-filtered payment methods data
        const actionCardData = {
          currency,
          userAllPaymentMethods: currencyMethods,
          userCurrentPaymentInfo: paymentMethodsStore.getDefaultMethodForCurrency(currency),
          canEditInline: true
        };
        
        debugLogger.log('🔍 [TransactionalChat] Creating action card with data:', actionCardData);
        
        // Add the action card to the feed
        addActionCard('paymentInfo', actionCardData);
        
        // Clear any previous form state to avoid stale data
        paymentIntegration.closePaymentDetailsForm();
        
        // Open payment form with the correct currency (for backup modal)
        await paymentIntegration.openPaymentDetailsForm(currentTransactionId, currency);
        showPaymentDetailsForm.value = true;
        
        // Debug log only in development environment
        debugLogger.log('🔍 [TransactionalChat] Payment action card created and form opened for currency:', currency);
        return;
      }
      
      // Call the real API for other actions
      let apiActionType = actionType;
      if (actionType === 'makePayment') {
        apiActionType = 'declareFirstPayerPayment';
      } else if (actionType === 'makeSecondPayment') {
        apiActionType = 'declareSecondPayerPayment';
      } else if (actionType === 'confirmFirstPaymentReceipt') {
        apiActionType = 'confirmFirstPayerPayment';
      }

      await transactionalChatApiService.performTransactionAction(currentTransactionId, apiActionType, data);
      
      // The store state will be updated via socket events from the backend
      // But we can also refetch to ensure consistency
      await fetchTransaction(currentTransactionId);
      
      // Clear pinned action after successful completion
      clearPinnedAction();
    } catch (error: any) {
      errorState.value = {
        hasError: true,
        message: error.message || 'Failed to perform action'
      };
    } finally {
      isLoading.value = false;
    }
  };

  const sendMessage = async (messageText: string) => {
    const authStore = useAuthStore();
    if (!authStore.user) return;

    try {
      // Get current transaction ID
      const currentTransactionId = getCurrentTransactionId();
      if (!currentTransactionId) {
        throw new Error('No active transaction found');
      }

      // Call the real API
      const messagePayload = await transactionalChatApiService.sendTransactionMessage(
        currentTransactionId,
        messageText
      );

      // Add message to feed immediately (it should also come via socket)
      const newMessage: FeedItem = {
        id: messagePayload.messageId,
        type: 'chat',
        timestamp: messagePayload.createdAt,
        content: messagePayload.content,
        sender: {
          id: messagePayload.sender.id,
          name: messagePayload.sender.username,
          isCurrentUser: true
        }
      };

      // Only add if not already present (avoid duplicates from socket)
      if (!feedItems.value.find(item => item.id === newMessage.id)) {
        feedItems.value.push(newMessage);
      }
    } catch (error: any) {
      errorState.value = {
        hasError: true,
        message: error.message || 'Failed to send message'
      };
    }
  };

  const startTimer = (seconds: number) => {
    timer.value = { isActive: true, remainingSeconds: seconds };
    
    if (timerInterval) {
      clearInterval(timerInterval);
    }

    timerInterval = setInterval(() => {
      if (timer.value.remainingSeconds > 0) {
        timer.value.remainingSeconds--;
      } else {
        stopTimer();
      }
    }, 1000);
  };

  const stopTimer = () => {
    timer.value = { isActive: false, remainingSeconds: 0 };
    if (timerInterval) {
      clearInterval(timerInterval);
      timerInterval = null;
    }
  };

  const addSystemLog = (messageKey: string, params?: any) => {
    const systemLog: FeedItem = {
      id: generateUniqueId('log'),
      type: 'systemLog',
      timestamp: new Date().toISOString(),
      message: messageKey,
      data: params
    };

    console.log('📝 Adding system log:', { id: systemLog.id, message: messageKey, params });
    feedItems.value.push(systemLog);
    console.log('📊 Total feed items after adding system log:', feedItems.value.length);
    
    // Force reactivity update
    feedItems.value = [...feedItems.value];
    console.log('🔄 Forced reactivity update, total items:', feedItems.value.length);
  };

  const addActionCard = (actionType: string, data?: any) => {
    // Remove existing action cards of the same type
    feedItems.value = feedItems.value.filter(item => 
      !(item.type === 'actionCard' && item.actionType === actionType)
    );

    const actionCard: FeedItem = {
      id: generateUniqueId('action'),
      type: 'actionCard',
      timestamp: new Date().toISOString(),
      actionType,
      data
    };

    feedItems.value.push(actionCard);

    // Set pinned action if this is a user action
    if (isUsersTurn.value && currentStep.value?.isUserAction) {
      setPinnedAction(actionType, actionCard.id);
    }
  };

  const setPinnedAction = (actionType: string, cardId: string) => {
    const step = currentStep.value;
    if (!step || !step.isUserAction) {
      pinnedAction.value = null;
      return;
    }

    pinnedAction.value = {
      title: step.titleKey,
      cardId,
      actionType,
      stepIndex: currentStepIndex.value
    };
    
    // Initially assume action card is visible (user just navigated to this step)
    isActionCardVisible.value = true;
  };

  const clearPinnedAction = () => {
    pinnedAction.value = null;
    isActionCardVisible.value = true; // Reset to visible state
  };
  
  const setActionCardVisibility = (isVisible: boolean) => {
    isActionCardVisible.value = isVisible;
  };

  const scrollToActionCard = async (cardId: string) => {
    console.log('📍 Scrolling to action card:', cardId);
    
    // Import the composable for DOM manipulation
    const { useScrollToElement } = await import('@/composables/useScrollToElement');
    const { scrollToElementByTestId, scrollToElementByCardId } = useScrollToElement();
    
    // Handle special cases for Dynamic Action Bar steps
    if (cardId.startsWith('dynamic-action-')) {
      // For steps handled by Dynamic Action Bar, scroll to the action bar
      const success = await scrollToElementByTestId('action-bar', {
        behavior: 'smooth',
        block: 'end',
        inline: 'nearest',
        highlightClass: 'highlight-action-bar',
        highlightDuration: 2000
      });
      
      if (success) {
        return;
      }
    }
    
    // Use the composable for regular action cards with retry mechanism
    await scrollToElementByCardId(cardId, {
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest',
      highlightClass: 'highlight-card',
      highlightDuration: 2000,
      retryDelay: 100,
      maxRetries: 1
    });
  };

  const clearError = () => {
    errorState.value = { hasError: false, message: '' };
  };

  const submitPaymentDetails = async (paymentDetails: any) => {
    isLoading.value = true;
    errorState.value = { hasError: false, message: '' };

    try {
      const currentTransactionId = getCurrentTransactionId();
      if (!currentTransactionId) {
        throw new Error('No active transaction found');
      }

      // Call the real API with payment details
      await transactionalChatApiService.performTransactionAction(currentTransactionId, 'paymentInfo', paymentDetails);
      
      // Hide the form and refresh transaction state
      showPaymentDetailsForm.value = false;
      await fetchTransaction(currentTransactionId);
      
      // Clear pinned action after successful completion
      clearPinnedAction();
    } catch (error: any) {
      errorState.value = {
        hasError: true,
        message: error.message || 'Failed to submit payment details'
      };
    } finally {
      isLoading.value = false;
    }
  };

  const closePaymentDetailsForm = () => {
    showPaymentDetailsForm.value = false;
  };

  const completePaymentMethodSelection = async (paymentMethodId: string) => {
    isLoading.value = true;
    errorState.value = { hasError: false, message: '' };

    try {
      const currentTransactionId = getCurrentTransactionId();
      if (!currentTransactionId) {
        throw new Error('No active transaction found');
      }

      // Call the API with the selected payment method
      await transactionalChatApiService.performTransactionAction(
        currentTransactionId, 
        'paymentInfo', 
        { paymentMethodId }
      );

      // Close the form immediately
      closePaymentDetailsForm();

      // Clear loading state to allow socket-triggered refreshes to work
      isLoading.value = false;

      // Set up one-time socket listener for immediate updates
      let updateReceived = false;
      const transactionToMonitor = currentTransactionId;
      
      const socketCleanup = centralizedSocketManager.once('TRANSACTION_STATUS_UPDATED', (payload: any) => {
        if (payload?.transactionId === transactionToMonitor) {
          console.log('✅ Received immediate socket update for payment method change');
          updateReceived = true;
        }
      });

      // Wait briefly for potential socket update, then fallback to manual refetch
      await new Promise<void>((resolve) => {
        // Wait up to 800ms for socket update (shorter timeout)
        setTimeout(() => {
          if (!updateReceived) {
            console.log('⚠️ No socket update received within 800ms, performing manual refetch');
            // Force a refresh even if loading
            debouncedFetchTransaction(transactionToMonitor, 50, true);
          }
          resolve();
        }, 800);
      });

      // Clean up the socket listener if it hasn't fired
      if (!updateReceived) {
        socketCleanup();
      }

      // Clear pinned action after successful completion
      clearPinnedAction();
    } catch (error: any) {
      errorState.value = {
        hasError: true,
        message: error.message || 'Failed to submit payment method'
      };
    } finally {
      // Ensure loading state is cleared
      isLoading.value = false;
    }
  };

  const reset = () => {
    console.log('🔄 RESETTING transactional chat store');
    
    // Clear socket listeners first to prevent any stale events
    clearSocketListeners();
    
    otherUser.value = null;
    transactionDetails.value = null;
    transactionStatus.value = null;
    currentTransactionIdRef.value = null;
    currentStepIndex.value = 0;
    feedItems.value = [];
    timer.value = { isActive: false, remainingSeconds: 0 };
    errorState.value = { hasError: false, message: '' };
    pinnedAction.value = null;
    chatSessionId.value = '';
    isLoading.value = false;
    currentTransactionId.value = '';
    idCounter = 0; // Reset ID counter
    stopTimer();
    
    // Clear debounce timeout
    if (fetchTransactionDebounceTimeout) {
      clearTimeout(fetchTransactionDebounceTimeout);
      fetchTransactionDebounceTimeout = null;
    }
  };

  return {
    // State
    otherUser,
    transactionDetails,
    transactionStatus,
    currentTransactionIdRef,
    currentStepIndex,
    steps,
    feedItems,
    timer,
    errorState,
    pinnedAction,
    isActionCardVisible,
    shouldShowPinnedBanner,
    chatSessionId,
    isLoading,
    showPaymentDetailsForm,
    isStatusBarShrunk,
    // Getters
    currentStep,
    isUsersTurn,
    activeActionCard,
    totalSteps,
    // Actions
    fetchTransaction,
    performAction,
    sendMessage,
    startTimer,
    stopTimer,
    addSystemLog,
    addActionCard,
    setPinnedAction,
    clearPinnedAction,
    setActionCardVisibility,
    scrollToActionCard,
    clearError,
    closePaymentDetailsForm,
    completePaymentMethodSelection,
    reset
  };
});
